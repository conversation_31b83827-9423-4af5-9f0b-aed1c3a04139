import React from 'react';
import { ArrowRight, Users, Target, Brain, Rocket, Shield, TrendingUp, Globe, MessageCircle } from 'lucide-react';

const AboutPage = () => {
  const teamMembers = [
    {
      name: '<PERSON>',
      role: 'Founder & CEO',
      bio: 'A seasoned AI engineer and crypto trader with a background in algorithmic finance. Formerly at Goldman Sachs, <PERSON> founded LightQuant to bring hedge fund-grade strategies to the public.',
      avatar: 'https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop&crop=face',
      gradient: 'from-blue-500 to-purple-500'
    },
    {
      name: '<PERSON>',
      role: 'Chief Technology Officer',
      bio: 'Ex-Google engineer with 10+ years building scalable cloud systems and trading infrastructure. Passionate about automation, reliability, and low-latency architecture.',
      avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop&crop=face',
      gradient: 'from-green-500 to-blue-500'
    },
    {
      name: 'Dr. <PERSON>',
      role: 'Head of Research & Machine Learning',
      bio: 'PhD in Quantitative Finance from MIT. Leads our bot development lab and ensures every strategy is scientifically validated with rigorous backtesting.',
      avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop&crop=face',
      gradient: 'from-purple-500 to-pink-500'
    }
  ];

  const focusAreas = [
    {
      icon: Brain,
      title: 'AI-Powered Strategies',
      description: 'Launching new machine learning-driven trading strategies monthly',
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/10',
      borderColor: 'border-blue-500/30'
    },
    {
      icon: Shield,
      title: 'Smart Risk Controls',
      description: 'Enhancing portfolio diversification and risk management tools',
      color: 'text-amber-400',
      bgColor: 'bg-amber-500/10',
      borderColor: 'border-amber-500/30'
    },
    {
      icon: TrendingUp,
      title: 'Mobile Experience',
      description: 'Launching a mobile app with real-time bot insights and controls',
      color: 'text-purple-400',
      bgColor: 'bg-purple-500/10',
      borderColor: 'border-purple-500/30'
    },
    {
      icon: Users,
      title: 'Trading Community',
      description: 'Building a community of smart, data-driven investors',
      color: 'text-green-400',
      bgColor: 'bg-green-500/10',
      borderColor: 'border-green-500/30'
    }
  ];

  return (
    <>
      {/* Hero Section */}
      <section className="relative pt-32 pb-16 bg-black overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-blue-600/20 to-transparent rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-amber-500/20 to-transparent rounded-full blur-3xl animate-float delay-1000"></div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-6 text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 animate-fade-in-up">
            About <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">LightQuant</span>
          </h1>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto animate-fade-in-up delay-200 text-balance">
            AI Crypto Bot Platform - Redefining automated trading through cutting-edge AI and machine learning
          </p>
        </div>
      </section>

      <div className="bg-gray-900">
        {/* Who We Are */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-6">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div className="animate-fade-in-up">
                <div className="flex items-center gap-3 mb-6">
                  <Rocket className="w-8 h-8 text-blue-400" />
                  <h2 className="text-4xl font-bold text-white">Who We Are</h2>
                </div>
                <div className="space-y-6 text-lg text-gray-300 leading-relaxed">
                  <p>
                    We are a team of data scientists, engineers, and crypto enthusiasts on a mission to redefine automated trading through cutting-edge AI and machine learning. At the core of our platform is a belief that intelligent automation can unlock superior trading performance while reducing human error and emotional bias.
                  </p>
                  <p>
                    Since day one, our goal has been to make AI trading accessible, transparent, and profitable — not just for hedge funds or quant teams, but for every crypto investor.
                  </p>
                </div>
              </div>
              
              <div className="relative animate-fade-in-up delay-300">
                <div className="bg-black border border-gray-800 rounded-3xl p-8 shadow-2xl">
                  <div className="grid grid-cols-2 gap-6">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-blue-500/10 border border-blue-500/30 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <Brain className="w-8 h-8 text-blue-400" />
                      </div>
                      <h3 className="font-bold text-white mb-2">AI-Driven</h3>
                      <p className="text-gray-400 text-sm">Machine learning at the core</p>
                    </div>
                    <div className="text-center">
                      <div className="w-16 h-16 bg-amber-500/10 border border-amber-500/30 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <Shield className="w-8 h-8 text-amber-400" />
                      </div>
                      <h3 className="font-bold text-white mb-2">Transparent</h3>
                      <p className="text-gray-400 text-sm">Full performance history</p>
                    </div>
                    <div className="text-center">
                      <div className="w-16 h-16 bg-purple-500/10 border border-purple-500/30 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <TrendingUp className="w-8 h-8 text-purple-400" />
                      </div>
                      <h3 className="font-bold text-white mb-2">Profitable</h3>
                      <p className="text-gray-400 text-sm">Data-driven results</p>
                    </div>
                    <div className="text-center">
                      <div className="w-16 h-16 bg-green-500/10 border border-green-500/30 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <Users className="w-8 h-8 text-green-400" />
                      </div>
                      <h3 className="font-bold text-white mb-2">Accessible</h3>
                      <p className="text-gray-400 text-sm">For every investor</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* What We Do */}
        <section className="py-20 bg-black">
          <div className="max-w-7xl mx-auto px-6">
            <div className="text-center mb-16">
              <div className="flex items-center justify-center gap-3 mb-6">
                <Brain className="w-8 h-8 text-blue-400" />
                <h2 className="text-4xl font-bold text-white">What We Do</h2>
              </div>
              <p className="text-xl text-gray-400 max-w-3xl mx-auto">
                Our platform allows users to follow and deploy high-performance trading bots that operate across major crypto exchanges like Binance, Bybit, and OKX.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  icon: '✅',
                  title: 'Trained on Real Market Data',
                  description: 'Every bot learns from actual market conditions and historical patterns'
                },
                {
                  icon: '✅',
                  title: 'Backtested & Risk-Assessed',
                  description: 'Rigorous testing and stress-testing across different market conditions'
                },
                {
                  icon: '✅',
                  title: 'Continuously Improved',
                  description: 'Machine learning algorithms that adapt and evolve with market changes'
                }
              ].map((feature, index) => (
                <div 
                  key={index}
                  className="group bg-gray-900 border border-gray-800 rounded-3xl p-8 hover:bg-gray-800 hover:border-gray-700 transition-all duration-500 transform hover:-translate-y-2 animate-scale-in"
                  style={{ animationDelay: `${index * 150}ms` }}
                >
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-bold text-white mb-3">{feature.title}</h3>
                  <p className="text-gray-400 leading-relaxed">{feature.description}</p>
                </div>
              ))}
            </div>

            <div className="text-center mt-12">
              <p className="text-lg text-gray-400 max-w-2xl mx-auto text-balance">
                We handle the complex tech under the hood so you can focus on what matters — growing your portfolio.
              </p>
            </div>
          </div>
        </section>

        {/* Mission */}
        <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="max-w-7xl mx-auto px-6 text-center text-white">
            <div className="flex items-center justify-center gap-3 mb-6">
              <Target className="w-8 h-8" />
              <h2 className="text-4xl font-bold">Our Mission</h2>
            </div>
            <p className="text-2xl mb-8 max-w-4xl mx-auto leading-relaxed">
              To empower crypto traders of all levels with AI-driven tools that maximize performance, minimize risk, and automate complex decision-making.
            </p>
            <p className="text-xl opacity-90 max-w-3xl mx-auto">
              We believe trading should be driven by data, not guesswork. That's why we invest heavily in research, infrastructure, and transparency — giving our users the edge they need in a highly volatile market.
            </p>
          </div>
        </section>

        {/* Leadership Team */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-6">
            <div className="text-center mb-16">
              <div className="flex items-center justify-center gap-3 mb-6">
                <Users className="w-8 h-8 text-blue-400" />
                <h2 className="text-4xl font-bold text-white">Leadership Team</h2>
              </div>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {teamMembers.map((member, index) => (
                <div 
                  key={index}
                  className="group bg-black border border-gray-800 rounded-3xl p-8 shadow-lg hover:border-gray-700 transition-all duration-500 animate-scale-in"
                  style={{ animationDelay: `${index * 150}ms` }}
                >
                  <div className="text-center mb-6">
                    <div className="relative inline-block">
                      <img 
                        src={member.avatar} 
                        alt={member.name}
                        className="w-24 h-24 rounded-full object-cover mx-auto mb-4 border-2 border-gray-700"
                      />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-1">{member.name}</h3>
                    <p className={`font-medium bg-gradient-to-r ${member.gradient} bg-clip-text text-transparent`}>
                      {member.role}
                    </p>
                  </div>
                  <p className="text-gray-400 leading-relaxed text-center">
                    {member.bio}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Where We're Going */}
        <section className="py-20 bg-black">
          <div className="max-w-7xl mx-auto px-6">
            <div className="text-center mb-16">
              <div className="flex items-center justify-center gap-3 mb-6">
                <Globe className="w-8 h-8 text-blue-400" />
                <h2 className="text-4xl font-bold text-white">Where We're Going</h2>
              </div>
              <p className="text-xl text-gray-400 max-w-3xl mx-auto">
                We're building the future of fully autonomous trading, starting with crypto — and expanding to any asset class where algorithms can outperform.
              </p>
            </div>

            <div className="mb-12">
              <h3 className="text-2xl font-bold text-white text-center mb-8">
                In 2025 and beyond, our focus areas include:
              </h3>
              
              <div className="grid md:grid-cols-2 gap-8">
                {focusAreas.map((area, index) => {
                  const Icon = area.icon;
                  return (
                    <div 
                      key={index}
                      className={`group flex items-start gap-6 bg-gray-900 border ${area.borderColor} rounded-2xl p-6 hover:bg-gray-800 transition-all duration-500 animate-slide-in-left`}
                      style={{ animationDelay: `${index * 150}ms` }}
                    >
                      <div className={`w-12 h-12 ${area.bgColor} border ${area.borderColor} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                        <Icon className={`w-6 h-6 ${area.color}`} />
                      </div>
                      <div>
                        <h4 className="text-lg font-bold text-white mb-2">{area.title}</h4>
                        <p className="text-gray-400">{area.description}</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </section>

        {/* Join Us CTA */}
        <section className="py-20 bg-gradient-to-br from-black via-gray-900 to-black">
          <div className="max-w-7xl mx-auto px-6 text-center text-white">
            <div className="flex items-center justify-center gap-3 mb-6">
              <MessageCircle className="w-8 h-8 text-blue-400" />
              <h2 className="text-4xl font-bold">Join Us</h2>
            </div>
            <p className="text-2xl mb-12 max-w-3xl mx-auto">
              Whether you're a beginner or an experienced trader, our platform was built to help you trade smarter, not harder.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="group bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 glow-blue">
                <span className="flex items-center gap-2">
                  Explore Bots
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </span>
              </button>
              
              <button className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-500 hover:to-emerald-500 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 glow-gold">
                Start Free Trial
              </button>
              
              <button className="border-2 border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white hover:border-gray-600 px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 transform hover:scale-105">
                Join Our Discord
              </button>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default AboutPage;