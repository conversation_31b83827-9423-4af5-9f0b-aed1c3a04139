import React from 'react';
import { <PERSON>, Shield, BarChart3, Zap, Network } from 'lucide-react';

const WhyChooseUs = () => {
  const features = [
    {
      icon: Brain,
      title: 'AI Optimization',
      description: 'Constantly improves strategy with market feedback',
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/10',
      borderColor: 'border-blue-500/30'
    },
    {
      icon: Shield,
      title: 'Risk Engine',
      description: 'Adaptive capital allocation & stop-loss',
      color: 'text-amber-400',
      bgColor: 'bg-amber-500/10',
      borderColor: 'border-amber-500/30'
    },
    {
      icon: BarChart3,
      title: 'Backtest Transparency',
      description: 'All bots show full historical metrics',
      color: 'text-purple-400',
      bgColor: 'bg-purple-500/10',
      borderColor: 'border-purple-500/30'
    },
    {
      icon: Zap,
      title: 'Auto Deployment',
      description: 'One-click to follow, no code required',
      color: 'text-green-400',
      bgColor: 'bg-green-500/10',
      borderColor: 'border-green-500/30'
    },
    {
      icon: Network,
      title: 'Multi-exchange Support',
      description: 'Binance, Bybit, OKX, and more coming',
      color: 'text-cyan-400',
      bgColor: 'bg-cyan-500/10',
      borderColor: 'border-cyan-500/30'
    }
  ];

  return (
    <section className="py-20 bg-gray-900">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4 animate-fade-in-up">
            🎯 Why Traders Trust Our Platform
          </h2>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.slice(0, 3).map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div
                key={index}
                className={`group bg-black border ${feature.borderColor} rounded-3xl p-8 hover:bg-gray-800/50 transition-all duration-500 animate-slide-in-left`}
                style={{ animationDelay: `${index * 150}ms` }}
              >
                <div className={`w-16 h-16 ${feature.bgColor} border ${feature.borderColor} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <Icon className={`w-8 h-8 ${feature.color}`} />
                </div>
                
                <h3 className="text-xl font-bold text-white mb-3">
                  ✅ {feature.title}
                </h3>
                
                <p className="text-gray-400 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            );
          })}
        </div>

        <div className="grid md:grid-cols-2 gap-8 mt-8 max-w-4xl mx-auto">
          {features.slice(3).map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div
                key={index + 3}
                className={`group bg-black border ${feature.borderColor} rounded-3xl p-8 hover:bg-gray-800/50 transition-all duration-500 animate-slide-in-right`}
                style={{ animationDelay: `${(index + 3) * 150}ms` }}
              >
                <div className={`w-16 h-16 ${feature.bgColor} border ${feature.borderColor} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <Icon className={`w-8 h-8 ${feature.color}`} />
                </div>
                
                <h3 className="text-xl font-bold text-white mb-3">
                  ✅ {feature.title}
                </h3>
                
                <p className="text-gray-400 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;