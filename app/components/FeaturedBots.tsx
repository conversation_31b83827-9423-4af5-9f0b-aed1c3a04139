import React from 'react';
import { TrendingUp, Shield, Activity, ArrowRight } from 'lucide-react';

const FeaturedBots = () => {
  const bots = [
    {
      name: 'AlphaX',
      roi: '+27%',
      period: '30 days',
      strategy: 'Momentum + AI signals',
      icon: TrendingUp,
      gradient: 'from-blue-500 to-cyan-500',
      bgColor: 'bg-blue-500/10',
      borderColor: 'border-blue-500/30',
      textColor: 'text-blue-400',
      buttonText: 'Follow Bot'
    },
    {
      name: 'BetaShield',
      roi: '+15%',
      period: 'low drawdown',
      strategy: 'Futures, Risk-Controlled',
      icon: Shield,
      gradient: 'from-amber-500 to-orange-500',
      bgColor: 'bg-amber-500/10',
      borderColor: 'border-amber-500/30',
      textColor: 'text-amber-400',
      buttonText: 'View Details'
    },
    {
      name: 'GammaQuant',
      roi: '+38%',
      period: 'sideways market',
      strategy: 'Mean-reversion + RL',
      icon: Activity,
      gradient: 'from-purple-500 to-pink-500',
      bgColor: 'bg-purple-500/10',
      borderColor: 'border-purple-500/30',
      textColor: 'text-purple-400',
      buttonText: 'Copy Strategy'
    }
  ];

  return (
    <section className="py-20 bg-gray-900">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4 animate-fade-in-up">
            🔥 Bots that Deliver Results
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto animate-fade-in-up delay-200">
            🧠 All bots are built by experts and powered by cutting-edge machine learning models.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {bots.map((bot, index) => {
            const Icon = bot.icon;
            return (
              <div
                key={index}
                className={`group bg-black border-2 ${bot.borderColor} rounded-3xl p-8 hover:bg-gray-900/50 transition-all duration-500 transform hover:-translate-y-2 animate-scale-in`}
                style={{ animationDelay: `${index * 200}ms` }}
              >
                <div className={`w-16 h-16 ${bot.bgColor} border ${bot.borderColor} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <Icon className={`w-8 h-8 ${bot.textColor}`} />
                </div>
                
                <h3 className="text-2xl font-bold text-white mb-2">{bot.name}</h3>
                
                <div className="mb-4">
                  <span className={`text-3xl font-bold bg-gradient-to-r ${bot.gradient} bg-clip-text text-transparent`}>
                    {bot.roi}
                  </span>
                  <span className="text-gray-400 ml-2">ROI in last {bot.period}</span>
                </div>
                
                <p className="text-gray-400 mb-6">
                  <span className="font-medium text-gray-300">Strategy:</span> {bot.strategy}
                </p>
                
                <button className={`w-full bg-gradient-to-r ${bot.gradient} text-white py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 group-hover:scale-105 flex items-center justify-center gap-2`}>
                  {bot.buttonText}
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                </button>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  );
};

export default FeaturedBots;