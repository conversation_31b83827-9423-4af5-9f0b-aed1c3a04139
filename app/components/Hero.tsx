import React from 'react';
import { ArrowR<PERSON>, TrendingUp, <PERSON>, <PERSON><PERSON>hart<PERSON>, Zap } from 'lucide-react';

const Hero = () => {
  return (
    <section className="relative min-h-screen bg-black overflow-hidden">
      {/* Background Grid Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>
      
      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/10 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute top-40 right-20 w-24 h-24 bg-amber-500/10 rounded-full blur-xl animate-pulse delay-1000"></div>
      <div className="absolute bottom-40 left-1/4 w-40 h-40 bg-blue-500/10 rounded-full blur-xl animate-pulse delay-2000"></div>

      {/* Gradient Orbs */}
      <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-blue-600/20 to-transparent rounded-full blur-3xl animate-float"></div>
      <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-amber-500/20 to-transparent rounded-full blur-3xl animate-float delay-1000"></div>

      <div className="relative z-10 max-w-7xl mx-auto px-6 pt-52 pb-16">
        <div className="text-center">
          {/* Main Headline */}
          <div className="mb-8 animate-fade-in-up">
            <div className="inline-flex items-center gap-2 bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-full px-4 py-2 mb-6">
              <Zap className="w-4 h-4 text-blue-400" />
              <span className="text-gray-300 text-sm font-medium">AI-Powered Trading Revolution</span>
            </div>
          </div>

          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight animate-fade-in-up delay-200">
            <span className="bg-gradient-to-r from-white via-blue-100 to-white bg-clip-text text-transparent">
              AI-Powered
            </span>
            <br />
            <span className="bg-gradient-to-r from-blue-400 to-amber-400 bg-clip-text text-transparent animate-gradient">
              Crypto Trading Bots
            </span>
          </h1>
          
          <p className="text-2xl md:text-3xl text-gray-300 mb-4 font-light animate-fade-in-up delay-300">
            Smarter. Faster. Fully Automated.
          </p>

          {/* Subheadline */}
          <p className="text-lg text-gray-400 mb-12 max-w-3xl mx-auto leading-relaxed animate-fade-in-up delay-400">
            Discover intelligent crypto trading bots trained on real market data.
            Optimize profits, reduce risk, and automate your strategy — all in one click.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16 animate-fade-in-up delay-500">
            <button className="group bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 glow-blue">
              <span className="flex items-center gap-2">
                🚀 Start Free Trial – 7 Days
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </span>
            </button>
            <button className="border-2 border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white hover:border-gray-600 px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 transform hover:scale-105">
              📈 View Bot Performance
            </button>
          </div>

          {/* Key Benefits */}
          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="bg-gray-900/30 backdrop-blur-sm border border-gray-800 rounded-2xl p-6 hover:bg-gray-800/50 hover:border-gray-700 transition-all duration-500 animate-slide-in-left delay-600 group">
              <TrendingUp className="w-8 h-8 text-blue-400 mb-4 mx-auto group-hover:scale-110 transition-transform duration-300" />
              <p className="text-gray-300 font-medium">🤖 AI-driven bots personalized to your risk profile</p>
            </div>
            <div className="bg-gray-900/30 backdrop-blur-sm border border-gray-800 rounded-2xl p-6 hover:bg-gray-800/50 hover:border-gray-700 transition-all duration-500 animate-scale-in delay-700 group">
              <Shield className="w-8 h-8 text-amber-400 mb-4 mx-auto group-hover:scale-110 transition-transform duration-300" />
              <p className="text-gray-300 font-medium">🔐 Secure API integration with Binance, Bybit, OKX</p>
            </div>
            <div className="bg-gray-900/30 backdrop-blur-sm border border-gray-800 rounded-2xl p-6 hover:bg-gray-800/50 hover:border-gray-700 transition-all duration-500 animate-slide-in-right delay-800 group">
              <BarChart3 className="w-8 h-8 text-blue-400 mb-4 mx-auto group-hover:scale-110 transition-transform duration-300" />
              <p className="text-gray-300 font-medium">📊 Real-time analytics and transparent performance reports</p>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Gradient */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-gray-900 to-transparent"></div>
    </section>
  );
};

export default Hero;